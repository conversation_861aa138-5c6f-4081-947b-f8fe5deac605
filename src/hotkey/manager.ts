import { globalShortcut } from 'electron';

export interface HotkeyConfig {
  accelerator: string;
  callback: () => void;
  description?: string;
}

export interface SupportedKey {
  key: string;
  displayName: string;
  category: 'modifier' | 'function' | 'alphanumeric' | 'special';
}

export class HotkeyManager {
  private registeredHotkeys: Map<string, HotkeyConfig> = new Map();
  private currentHotkey: string | null = null;

  // Supported modifier keys
  private static readonly MODIFIERS: SupportedKey[] = [
    { key: 'CommandOrControl', displayName: 'Ctrl/Cmd', category: 'modifier' },
    { key: 'Shift', displayName: 'Shift', category: 'modifier' },
    { key: 'Alt', displayName: 'Alt', category: 'modifier' },
    { key: 'Super', displayName: 'Super', category: 'modifier' }, // Windows key on Windows, Cmd on macOS
  ];

  // Supported function keys
  private static readonly FUNCTION_KEYS: SupportedKey[] = [
    { key: 'F1', displayName: 'F1', category: 'function' },
    { key: 'F2', displayName: 'F2', category: 'function' },
    { key: 'F3', displayName: 'F3', category: 'function' },
    { key: 'F4', displayName: 'F4', category: 'function' },
    { key: 'F5', displayName: 'F5', category: 'function' },
    { key: 'F6', displayName: 'F6', category: 'function' },
    { key: 'F7', displayName: 'F7', category: 'function' },
    { key: 'F8', displayName: 'F8', category: 'function' },
    { key: 'F9', displayName: 'F9', category: 'function' },
    { key: 'F10', displayName: 'F10', category: 'function' },
    { key: 'F11', displayName: 'F11', category: 'function' },
    { key: 'F12', displayName: 'F12', category: 'function' },
  ];

  // Supported alphanumeric keys
  private static readonly ALPHANUMERIC_KEYS: SupportedKey[] = [
    ...Array.from({ length: 26 }, (_, i) => ({
      key: String.fromCharCode(65 + i),
      displayName: String.fromCharCode(65 + i),
      category: 'alphanumeric' as const,
    })),
    ...Array.from({ length: 10 }, (_, i) => ({
      key: i.toString(),
      displayName: i.toString(),
      category: 'alphanumeric' as const,
    })),
  ];

  // Supported special keys
  private static readonly SPECIAL_KEYS: SupportedKey[] = [
    { key: 'Space', displayName: 'Space', category: 'special' },
    { key: 'Tab', displayName: 'Tab', category: 'special' },
    { key: 'Enter', displayName: 'Enter', category: 'special' },
    { key: 'Escape', displayName: 'Esc', category: 'special' },
    { key: 'Backspace', displayName: 'Backspace', category: 'special' },
    { key: 'Delete', displayName: 'Delete', category: 'special' },
    { key: 'Insert', displayName: 'Insert', category: 'special' },
    { key: 'Home', displayName: 'Home', category: 'special' },
    { key: 'End', displayName: 'End', category: 'special' },
    { key: 'PageUp', displayName: 'Page Up', category: 'special' },
    { key: 'PageDown', displayName: 'Page Down', category: 'special' },
    { key: 'Up', displayName: '↑', category: 'special' },
    { key: 'Down', displayName: '↓', category: 'special' },
    { key: 'Left', displayName: '←', category: 'special' },
    { key: 'Right', displayName: '→', category: 'special' },
  ];

  // System-reserved hotkeys that should not be allowed
  private static readonly RESERVED_HOTKEYS = [
    'CommandOrControl+C',
    'CommandOrControl+V',
    'CommandOrControl+X',
    'CommandOrControl+Z',
    'CommandOrControl+Y',
    'CommandOrControl+A',
    'CommandOrControl+S',
    'CommandOrControl+O',
    'CommandOrControl+N',
    'CommandOrControl+W',
    'CommandOrControl+Q',
    'CommandOrControl+R',
    'CommandOrControl+T',
    'CommandOrControl+F',
    'CommandOrControl+H',
    'Alt+F4',
    'CommandOrControl+Alt+Delete',
    'CommandOrControl+Shift+Escape',
  ];

  /**
   * Get all supported keys organized by category
   */
  static getSupportedKeys(): Record<string, SupportedKey[]> {
    return {
      modifiers: HotkeyManager.MODIFIERS,
      function: HotkeyManager.FUNCTION_KEYS,
      alphanumeric: HotkeyManager.ALPHANUMERIC_KEYS,
      special: HotkeyManager.SPECIAL_KEYS,
    };
  }

  /**
   * Validate a hotkey accelerator string
   */
  static validateAccelerator(accelerator: string): { valid: boolean; error?: string } {
    if (!accelerator || typeof accelerator !== 'string') {
      return { valid: false, error: 'Accelerator must be a non-empty string' };
    }

    // Check if it's a reserved hotkey
    if (HotkeyManager.RESERVED_HOTKEYS.includes(accelerator)) {
      return { valid: false, error: 'This hotkey is reserved by the system' };
    }

    const parts = accelerator.split('+');
    
    if (parts.length < 2) {
      return { valid: false, error: 'Hotkey must include at least one modifier key' };
    }

    const modifiers = parts.slice(0, -1);
    const mainKey = parts[parts.length - 1];

    // Validate modifiers
    const validModifiers = HotkeyManager.MODIFIERS.map(m => m.key);
    for (const modifier of modifiers) {
      if (!validModifiers.includes(modifier)) {
        return { valid: false, error: `Invalid modifier: ${modifier}` };
      }
    }

    // Validate main key
    const allKeys = [
      ...HotkeyManager.FUNCTION_KEYS,
      ...HotkeyManager.ALPHANUMERIC_KEYS,
      ...HotkeyManager.SPECIAL_KEYS,
    ];
    const validKeys = allKeys.map(k => k.key);
    
    if (!validKeys.includes(mainKey)) {
      return { valid: false, error: `Invalid key: ${mainKey}` };
    }

    return { valid: true };
  }

  /**
   * Format accelerator for display
   */
  static formatAcceleratorForDisplay(accelerator: string): string {
    if (!accelerator) return '';

    const parts = accelerator.split('+');
    const displayParts: string[] = [];

    for (const part of parts) {
      // Find the display name for this part
      const allKeys = [
        ...HotkeyManager.MODIFIERS,
        ...HotkeyManager.FUNCTION_KEYS,
        ...HotkeyManager.ALPHANUMERIC_KEYS,
        ...HotkeyManager.SPECIAL_KEYS,
      ];
      
      const keyInfo = allKeys.find(k => k.key === part);
      displayParts.push(keyInfo ? keyInfo.displayName : part);
    }

    return displayParts.join(' + ');
  }

  /**
   * Register a global hotkey
   */
  async registerHotkey(config: HotkeyConfig): Promise<{ success: boolean; error?: string }> {
    const validation = HotkeyManager.validateAccelerator(config.accelerator);
    if (!validation.valid) {
      return { success: false, error: validation.error };
    }

    try {
      // Unregister existing hotkey if any
      if (this.currentHotkey) {
        this.unregisterHotkey(this.currentHotkey);
      }

      // Try to register the new hotkey
      const success = globalShortcut.register(config.accelerator, config.callback);
      
      if (!success) {
        return { 
          success: false, 
          error: 'Failed to register hotkey - it may already be in use by another application' 
        };
      }

      // Store the registration
      this.registeredHotkeys.set(config.accelerator, config);
      this.currentHotkey = config.accelerator;

      console.log(`✅ Hotkey registered: ${config.accelerator}`);
      return { success: true };

    } catch (error) {
      console.error('Failed to register hotkey:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error occurred' 
      };
    }
  }

  /**
   * Unregister a specific hotkey
   */
  unregisterHotkey(accelerator: string): boolean {
    try {
      globalShortcut.unregister(accelerator);
      this.registeredHotkeys.delete(accelerator);
      
      if (this.currentHotkey === accelerator) {
        this.currentHotkey = null;
      }

      console.log(`🔓 Hotkey unregistered: ${accelerator}`);
      return true;
    } catch (error) {
      console.error('Failed to unregister hotkey:', error);
      return false;
    }
  }

  /**
   * Unregister all hotkeys
   */
  unregisterAll(): void {
    try {
      globalShortcut.unregisterAll();
      this.registeredHotkeys.clear();
      this.currentHotkey = null;
      console.log('🔓 All hotkeys unregistered');
    } catch (error) {
      console.error('Failed to unregister all hotkeys:', error);
    }
  }

  /**
   * Check if a hotkey is currently registered
   */
  isRegistered(accelerator: string): boolean {
    return globalShortcut.isRegistered(accelerator);
  }

  /**
   * Get the currently registered hotkey
   */
  getCurrentHotkey(): string | null {
    return this.currentHotkey;
  }

  /**
   * Get all registered hotkeys
   */
  getRegisteredHotkeys(): Map<string, HotkeyConfig> {
    return new Map(this.registeredHotkeys);
  }

  /**
   * Test if a hotkey can be registered (without actually registering it)
   */
  async testHotkey(accelerator: string): Promise<{ available: boolean; error?: string }> {
    const validation = HotkeyManager.validateAccelerator(accelerator);
    if (!validation.valid) {
      return { available: false, error: validation.error };
    }

    // Check if already registered by this app
    if (this.isRegistered(accelerator)) {
      return { available: false, error: 'Hotkey is already registered by this application' };
    }

    // Try to register temporarily to test availability
    try {
      const testCallback = () => {}; // Empty callback for testing
      const success = globalShortcut.register(accelerator, testCallback);
      
      if (success) {
        // Immediately unregister the test hotkey
        globalShortcut.unregister(accelerator);
        return { available: true };
      } else {
        return { available: false, error: 'Hotkey is already in use by another application' };
      }
    } catch (error) {
      return { 
        available: false, 
        error: error instanceof Error ? error.message : 'Unknown error occurred' 
      };
    }
  }

  /**
   * Get suggested default hotkeys that are likely to be available
   */
  static getDefaultHotkeySuggestions(): string[] {
    return [
      'CommandOrControl+Shift+Space',
      'CommandOrControl+Alt+Space',
      'F2',
      'F3',
      'F4',
      'CommandOrControl+Shift+V',
      'CommandOrControl+Shift+M',
      'Alt+Space',
    ];
  }

  /**
   * Cleanup - unregister all hotkeys
   */
  cleanup(): void {
    this.unregisterAll();
  }
}
