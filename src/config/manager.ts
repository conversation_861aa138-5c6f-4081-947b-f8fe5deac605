import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { app } from 'electron';

export interface AppConfig {
  // API Configuration
  openaiApiKey: string;
  
  // Hotkey Configuration
  hotkey: string;
  
  // Audio Configuration
  audioSettings: {
    sampleRate: number;
    channels: number;
    bitDepth: number;
    inputDevice?: string;
  };
  
  // Speech Recognition Configuration
  speechSettings: {
    language: string;
    temperature: number;
    prompt?: string;
  };
  
  // AI Features Configuration
  aiSettings: {
    enabled: boolean;
    model: string;
    maxTokens: number;
    temperature: number;
  };
  
  // UI Configuration
  uiSettings: {
    theme: 'light' | 'dark' | 'system';
    showNotifications: boolean;
    minimizeToTray: boolean;
    startMinimized: boolean;
  };
  
  // Advanced Configuration
  advanced: {
    autoStart: boolean;
    logLevel: 'error' | 'warn' | 'info' | 'debug';
    maxRecordingDuration: number; // in seconds
    silenceDetection: boolean;
    silenceThreshold: number;
  };
}

export class ConfigManager {
  private configPath: string;
  private config: AppConfig;
  private defaultConfig: AppConfig = {
    openaiApiKey: '',
    hotkey: 'F2',
    audioSettings: {
      sampleRate: 16000,
      channels: 1,
      bitDepth: 16
    },
    speechSettings: {
      language: 'en',
      temperature: 0.0
    },
    aiSettings: {
      enabled: true,
      model: 'gpt-4o',
      maxTokens: 1000,
      temperature: 0.7
    },
    uiSettings: {
      theme: 'system',
      showNotifications: true,
      minimizeToTray: true,
      startMinimized: false
    },
    advanced: {
      autoStart: false,
      logLevel: 'info',
      maxRecordingDuration: 60,
      silenceDetection: false,
      silenceThreshold: 0.1
    }
  };

  constructor() {
    this.configPath = this.getConfigPath();
    this.config = this.loadConfig();
  }

  private getConfigPath(): string {
    const userDataPath = app.getPath('userData');
    return path.join(userDataPath, 'config.json');
  }

  private loadConfig(): AppConfig {
    try {
      if (fs.existsSync(this.configPath)) {
        const configData = fs.readFileSync(this.configPath, 'utf8');
        const loadedConfig = JSON.parse(configData);
        
        // Merge with default config to ensure all properties exist
        return this.mergeConfig(this.defaultConfig, loadedConfig);
      }
    } catch (error) {
      console.error('Failed to load config:', error);
    }
    
    // Return default config if loading fails
    return { ...this.defaultConfig };
  }

  private mergeConfig(defaultConfig: AppConfig, userConfig: any): AppConfig {
    const merged = { ...defaultConfig } as any;

    // Recursively merge nested objects
    for (const key in userConfig) {
      if (userConfig.hasOwnProperty(key)) {
        if (typeof userConfig[key] === 'object' && userConfig[key] !== null && !Array.isArray(userConfig[key])) {
          merged[key] = { ...merged[key], ...userConfig[key] };
        } else {
          merged[key] = userConfig[key];
        }
      }
    }

    return merged as AppConfig;
  }

  saveConfig(): boolean {
    try {
      // Ensure the directory exists
      const configDir = path.dirname(this.configPath);
      if (!fs.existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true });
      }
      
      // Write config to file
      fs.writeFileSync(this.configPath, JSON.stringify(this.config, null, 2), 'utf8');
      return true;
    } catch (error) {
      console.error('Failed to save config:', error);
      return false;
    }
  }

  getConfig(): AppConfig {
    return { ...this.config };
  }

  updateConfig(updates: Partial<AppConfig>): boolean {
    try {
      // Merge updates with current config
      this.config = this.mergeConfig(this.config, updates);
      
      // Save to file
      return this.saveConfig();
    } catch (error) {
      console.error('Failed to update config:', error);
      return false;
    }
  }

  // Specific getters for commonly used settings
  getApiKey(): string {
    return this.config.openaiApiKey;
  }

  setApiKey(apiKey: string): boolean {
    return this.updateConfig({ openaiApiKey: apiKey });
  }

  getHotkey(): string {
    return this.config.hotkey;
  }

  setHotkey(hotkey: string): boolean {
    return this.updateConfig({ hotkey });
  }

  getLanguage(): string {
    return this.config.speechSettings.language;
  }

  setLanguage(language: string): boolean {
    return this.updateConfig({
      speechSettings: { ...this.config.speechSettings, language }
    });
  }

  // Method to reset config to defaults
  resetToDefaults(): boolean {
    this.config = { ...this.defaultConfig };
    return this.saveConfig();
  }

  // Method to export config
  exportConfig(): string {
    // Create a sanitized version without sensitive data
    const exportConfig = { ...this.config };
    exportConfig.openaiApiKey = exportConfig.openaiApiKey ? '[REDACTED]' : '';
    
    return JSON.stringify(exportConfig, null, 2);
  }

  // Method to import config
  importConfig(configJson: string): boolean {
    try {
      const importedConfig = JSON.parse(configJson);
      
      // Validate the imported config structure
      if (this.validateConfig(importedConfig)) {
        this.config = this.mergeConfig(this.defaultConfig, importedConfig);
        return this.saveConfig();
      } else {
        throw new Error('Invalid config structure');
      }
    } catch (error) {
      console.error('Failed to import config:', error);
      return false;
    }
  }

  private validateConfig(config: any): boolean {
    // Basic validation to ensure the config has the expected structure
    if (typeof config !== 'object' || config === null) {
      return false;
    }
    
    // Check for required top-level properties
    const requiredProps = ['audioSettings', 'speechSettings', 'aiSettings', 'uiSettings', 'advanced'];
    for (const prop of requiredProps) {
      if (!(prop in config) || typeof config[prop] !== 'object') {
        return false;
      }
    }
    
    return true;
  }

  // Method to get config file path (for debugging)
  getConfigFilePath(): string {
    return this.configPath;
  }

  // Method to check if config file exists
  configFileExists(): boolean {
    return fs.existsSync(this.configPath);
  }

  // Method to backup current config
  backupConfig(): string | null {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = this.configPath.replace('.json', `_backup_${timestamp}.json`);
      
      fs.copyFileSync(this.configPath, backupPath);
      return backupPath;
    } catch (error) {
      console.error('Failed to backup config:', error);
      return null;
    }
  }

  // Method to get available audio input devices
  async getAudioInputDevices(): Promise<Array<{id: string, name: string}>> {
    // This would need to be implemented with actual audio device enumeration
    // For now, return a placeholder
    return [
      { id: 'default', name: 'Default Microphone' },
      { id: 'system', name: 'System Audio' }
    ];
  }

  // Method to validate API key format
  validateApiKey(apiKey: string): boolean {
    // OpenAI API keys start with 'sk-' followed by base64-like characters
    // Updated format includes hyphens, underscores, and longer length
    return /^sk-[a-zA-Z0-9_-]{32,}$/.test(apiKey);
  }

  // Method to get supported hotkey combinations
  getSupportedHotkeys(): string[] {
    return [
      'CommandOrControl+Shift',
      'CommandOrControl+Alt',
      'CommandOrControl+Space',
      'Alt+Space',
      'F1',
      'F2',
      'F3',
      'F4'
    ];
  }
}
