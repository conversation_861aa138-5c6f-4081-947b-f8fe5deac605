import { app, BrowserWindow, ipcMain, Tray, Menu, globalShortcut, dialog } from 'electron';

// Add a custom property to track app quitting state
let isQuiting = false;
import * as path from 'path';
import { AudioRecorder } from './audio/recorder';
import { SpeechRecognizer } from './speech/recognizer';
import { TextInserter } from './text/inserter';
import { ConfigManager } from './config/manager';

class WhisperTypingApp {
  private mainWindow: BrowserWindow | null = null;
  private tray: Tray | null = null;
  private audioRecorder: AudioRecorder;
  private speechRecognizer: SpeechRecognizer;
  private textInserter: TextInserter;
  private configManager: ConfigManager;
  private isRecording = false;

  constructor() {
    this.audioRecorder = new AudioRecorder();
    this.speechRecognizer = new SpeechRecognizer();
    this.textInserter = new TextInserter();
    this.configManager = new ConfigManager();

    // Set API key from config
    const apiKey = this.configManager.getApiKey();
    if (apiKey) {
      this.speechRecognizer.setApiKey(apiKey);
    }
  }

  async initialize() {
    // Setup IPC handlers first, before app is ready
    this.setupIpcHandlers();

    await app.whenReady();

    this.createWindow();
    this.createTray();
    this.registerSimpleHotkey();

    // Hide window on startup (run in background)
    if (this.mainWindow) {
      this.mainWindow.hide();
    }
  }

  private createWindow() {
    this.mainWindow = new BrowserWindow({
      width: 400,
      height: 600,
      show: false,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false
      },
      icon: path.join(__dirname, '../assets/icon.png'),
      title: 'WhisperTyping Clone'
    });

    // Load the HTML file
    this.mainWindow.loadFile(path.join(__dirname, '../assets/index.html'));

    // Handle window closed
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // Hide window instead of closing when user clicks X
    this.mainWindow.on('close', (event) => {
      if (!isQuiting) {
        event.preventDefault();
        this.mainWindow?.hide();
      }
    });
  }

  private createTray() {
    try {
      const iconPath = path.join(__dirname, '../assets/tray-icon.png');
      this.tray = new Tray(iconPath);
    
    const contextMenu = Menu.buildFromTemplate([
      {
        label: 'Show Window',
        click: () => {
          this.mainWindow?.show();
        }
      },
      {
        label: 'Start/Stop Recording',
        click: () => {
          this.toggleRecording();
        }
      },
      { type: 'separator' },
      {
        label: 'Settings',
        click: () => {
          this.mainWindow?.show();
        }
      },
      { type: 'separator' },
      {
        label: 'Quit',
        click: () => {
          isQuiting = true;
          app.quit();
        }
      }
    ]);

    this.tray.setContextMenu(contextMenu);
    this.tray.setToolTip('WhisperTyping Clone');
    
      // Show window on tray click
      this.tray.on('click', () => {
        this.mainWindow?.show();
      });
    } catch (error) {
      console.error('Failed to create tray icon:', error);
      // Continue without tray icon if creation fails
    }
  }

  private registerSimpleHotkey() {
    try {
      // Use F2 - simple, safe, and rarely conflicts with other apps
      const hotkey = 'F2';

      const success = globalShortcut.register(hotkey, () => {
        console.log('F2 pressed - toggling recording');
        this.toggleRecording();
      });

      if (success) {
        console.log(`✅ Simple hotkey registered: ${hotkey}`);
        console.log('Press F2 to start/stop recording');
      } else {
        console.warn(`❌ Failed to register hotkey: ${hotkey}`);
      }
    } catch (error) {
      console.error('Failed to register hotkey:', error);
    }
  }


  private async toggleRecording() {
    if (this.isRecording) {
      await this.stopRecording();
    } else {
      await this.startRecording();
    }
  }

  private setupIpcHandlers() {
    ipcMain.handle('start-recording', async () => {
      return await this.startRecording();
    });

    ipcMain.handle('stop-recording', async () => {
      return await this.stopRecording();
    });

    ipcMain.handle('get-config', () => {
      return this.configManager.getConfig();
    });

    ipcMain.handle('update-config', (event, config) => {
      const success = this.configManager.updateConfig(config);
      if (success && config.openaiApiKey) {
        // Update the speech recognizer with the new API key
        this.speechRecognizer.setApiKey(config.openaiApiKey);
      }
      return success;
    });

    ipcMain.handle('test-api-key', async (event, apiKey) => {
      return await this.speechRecognizer.testApiKey(apiKey);
    });

    ipcMain.handle('update-hotkey', (event, newHotkey) => {
      // For now, we only support Ctrl+Shift, so just acknowledge the request
      console.log(`Hotkey update requested: ${newHotkey}, but currently locked to F2`);
      return true;
    });
  }

  private async startRecording(): Promise<boolean> {
    try {
      if (this.isRecording) return false;
      
      console.log('Starting recording...');
      this.isRecording = true;
      
      // Update tray icon to show recording state
      this.updateTrayIcon(true);
      
      // Start audio recording
      await this.audioRecorder.start();
      
      // Notify renderer process
      this.mainWindow?.webContents.send('recording-started');
      
      return true;
    } catch (error) {
      console.error('Failed to start recording:', error);
      this.isRecording = false;
      this.updateTrayIcon(false);
      return false;
    }
  }

  private async stopRecording(): Promise<boolean> {
    try {
      if (!this.isRecording) return false;
      
      console.log('Stopping recording...');
      this.isRecording = false;
      

      
      // Update tray icon
      this.updateTrayIcon(false);
      
      // Stop audio recording and get the audio data
      const audioData = await this.audioRecorder.stop();
      
      if (audioData) {
        // Send audio to speech recognition
        const transcription = await this.speechRecognizer.transcribe(audioData);

        if (transcription) {
          // Check if this is an AI command
          const aiCommand = this.speechRecognizer.detectAICommand(transcription);

          if (aiCommand) {
            console.log(`Detected AI command: ${aiCommand.mode} - ${aiCommand.prompt}`);

            // Process with AI
            const aiResponse = await this.speechRecognizer.processWithAI(transcription, aiCommand);

            if (aiResponse) {
              // Insert the AI-generated response
              await this.textInserter.insertText(aiResponse);

              // Notify renderer process with AI response
              this.mainWindow?.webContents.send('ai-response-complete', {
                mode: aiCommand.mode,
                prompt: aiCommand.prompt,
                response: aiResponse,
                originalTranscription: transcription
              });
            }
          } else {
            // Regular transcription - insert the transcribed text directly
            await this.textInserter.insertText(transcription);

            // Notify renderer process
            this.mainWindow?.webContents.send('transcription-complete', transcription);
          }
        }
      }
      
      // Notify renderer process
      this.mainWindow?.webContents.send('recording-stopped');
      
      return true;
    } catch (error) {
      console.error('Failed to stop recording:', error);
      this.isRecording = false;
      this.updateTrayIcon(false);
      return false;
    }
  }

  private updateTrayIcon(recording: boolean) {
    const iconName = recording ? 'tray-icon-recording.png' : 'tray-icon.png';
    const iconPath = path.join(__dirname, '../assets', iconName);
    this.tray?.setImage(iconPath);
  }

  async cleanup() {
    // Unregister global shortcuts
    globalShortcut.unregisterAll();

    // Clean up resources
    await this.audioRecorder.cleanup();
  }
}

// App event handlers
app.on('ready', async () => {
  const whisperApp = new WhisperTypingApp();
  await whisperApp.initialize();
});

app.on('window-all-closed', () => {
  // On macOS, keep the app running even when all windows are closed
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // On macOS, re-create window when dock icon is clicked
  if (BrowserWindow.getAllWindows().length === 0) {
    const whisperApp = new WhisperTypingApp();
    whisperApp.initialize();
  }
});

app.on('before-quit', async () => {
  isQuiting = true;
});
