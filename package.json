{"name": "whisper-typing-clone", "version": "1.0.0", "description": "Cross-platform speech-to-text application inspired by WhisperTyping", "main": "dist/main.js", "scripts": {"build": "tsc", "start": "npm run build && electron dist/main.js", "dev": "tsc && electron dist/main.js", "pack": "electron-builder", "dist": "electron-builder --publish=never", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["speech-to-text", "whisper", "electron", "cross-platform", "voice-typing"], "author": "Your Name", "license": "MIT", "build": {"appId": "com.yourcompany.whispertyping", "productName": "Whisper<PERSON><PERSON><PERSON>", "directories": {"output": "release"}, "files": ["dist/**/*", "assets/**/*"], "mac": {"category": "public.app-category.productivity"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}, "devDependencies": {"@types/node": "^24.0.13", "electron": "^37.2.1", "electron-builder": "^26.0.12", "typescript": "^5.8.3"}, "dependencies": {"@types/form-data": "^2.5.2", "axios": "^1.10.0", "form-data": "^4.0.3", "node-record-lpcm16": "^1.0.1"}}