<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhisperTyping Clone</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }

        .header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e0e0e0;
        }

        .header h1 {
            color: #4a5568;
            font-size: 24px;
            margin-bottom: 8px;
        }

        .header p {
            color: #718096;
            font-size: 14px;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .status-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            background: #e53e3e;
            transition: background 0.3s ease;
        }

        .status-dot.recording {
            background: #38a169;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .status-text {
            font-weight: 500;
            color: #4a5568;
        }

        .controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-bottom: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: #4299e1;
            color: white;
        }

        .btn-primary:hover {
            background: #3182ce;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn-secondary:hover {
            background: #cbd5e0;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .config-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .config-section h3 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #4a5568;
            font-weight: 500;
            font-size: 14px;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        .hotkey-display {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 10px;
            text-align: center;
            font-family: monospace;
            font-weight: bold;
            color: #4a5568;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .hotkey-display:hover {
            background: #edf2f7;
            border-color: #cbd5e0;
        }

        .transcription-area {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .transcription-area h3 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .transcription-text {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            min-height: 100px;
            font-size: 14px;
            line-height: 1.5;
            color: #4a5568;
        }

        .footer {
            padding: 15px 20px;
            border-top: 1px solid #e0e0e0;
            text-align: center;
            font-size: 12px;
            color: #718096;
        }

        .hidden {
            display: none;
        }

        .error {
            color: #e53e3e;
            font-size: 12px;
            margin-top: 5px;
        }

        .success {
            color: #38a169;
            font-size: 12px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>WhisperTyping Clone</h1>
            <p>AI-powered speech-to-text with global hotkeys</p>
        </div>

        <div class="main-content">
            <!-- Status Section -->
            <div class="status-section">
                <div class="status-indicator">
                    <div class="status-dot" id="statusDot"></div>
                    <span class="status-text" id="statusText">Ready</span>
                </div>
                <div class="controls">
                    <button class="btn btn-primary" id="recordBtn">Start Recording</button>
                    <button class="btn btn-secondary" id="settingsBtn">Settings</button>
                </div>
            </div>

            <!-- Configuration Section -->
            <div class="config-section" id="configSection">
                <h3>Configuration</h3>
                
                <div class="form-group">
                    <label for="apiKey">OpenAI API Key:</label>
                    <input type="password" id="apiKey" placeholder="sk-...">
                    <div id="apiKeyError" class="error hidden">Invalid API key format</div>
                    <div id="apiKeySuccess" class="success hidden">API key validated successfully</div>
                </div>

                <div class="form-group">
                    <label for="language">Language:</label>
                    <select id="language">
                        <option value="en">English</option>
                        <option value="es">Spanish</option>
                        <option value="fr">French</option>
                        <option value="de">German</option>
                        <option value="zh">Chinese</option>
                        <option value="ja">Japanese</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>Global Hotkey:</label>
                    <div class="hotkey-display" id="hotkeyDisplay">F2</div>
                    <p style="font-size: 12px; color: #718096; margin-top: 5px;">
                        Press F2 to start/stop recording (simple and safe!)
                    </p>
                </div>

                <button class="btn btn-primary" id="saveConfigBtn">Save Configuration</button>
            </div>

            <!-- AI Commands Help -->
            <div class="config-section">
                <h3>AI Voice Commands</h3>
                <div style="font-size: 12px; color: #4a5568; line-height: 1.4;">
                    <strong>Write Mode:</strong> "Write an answer to the message in my clipboard, and tell them politely that their offer is not as compelling as competing offers"<br><br>
                    <strong>Answer Mode:</strong> "Answer the question: How tall is the Eiffel tower?"<br><br>
                    <strong>Rewrite Mode:</strong> "Rewrite this to Portuguese" or "Rewrite this to bullet points"<br><br>
                    <strong>Reply Mode:</strong> "Reply that we don't offer these services, in a polite way"
                </div>
            </div>

            <!-- Transcription Area -->
            <div class="transcription-area">
                <h3>Last Transcription / AI Response</h3>
                <div class="transcription-text" id="transcriptionText">
                    Transcribed text or AI responses will appear here...
                </div>
            </div>
        </div>

        <div class="footer">
            Press <strong>F2</strong> to toggle recording • Text will be automatically inserted
        </div>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
