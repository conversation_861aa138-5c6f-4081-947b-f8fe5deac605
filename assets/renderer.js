const { ipc<PERSON><PERSON><PERSON> } = require('electron');

class RendererApp {
    constructor() {
        this.isRecording = false;
        this.config = null;
        this.initializeElements();
        this.setupEventListeners();
        this.loadConfiguration();
    }

    initializeElements() {
        // Status elements
        this.statusDot = document.getElementById('statusDot');
        this.statusText = document.getElementById('statusText');
        
        // Control elements
        this.recordBtn = document.getElementById('recordBtn');
        this.settingsBtn = document.getElementById('settingsBtn');
        this.saveConfigBtn = document.getElementById('saveConfigBtn');
        
        // Configuration elements
        this.configSection = document.getElementById('configSection');
        this.apiKeyInput = document.getElementById('apiKey');
        this.languageSelect = document.getElementById('language');
        this.hotkeyDisplay = document.getElementById('hotkeyDisplay');
        
        // Transcription elements
        this.transcriptionText = document.getElementById('transcriptionText');
        
        // Error/success elements
        this.apiKeyError = document.getElementById('apiKeyError');
        this.apiKeySuccess = document.getElementById('apiKeySuccess');
        
        // Hotkey configuration state
        this.isCapturingHotkey = false;
        this.capturedKeys = new Set();
    }

    setupEventListeners() {
        // Button event listeners
        this.recordBtn.addEventListener('click', () => this.toggleRecording());
        this.settingsBtn.addEventListener('click', () => this.toggleSettings());
        this.saveConfigBtn.addEventListener('click', () => this.saveConfiguration());
        
        // API key validation
        this.apiKeyInput.addEventListener('blur', () => this.validateApiKey());
        this.apiKeyInput.addEventListener('input', () => this.clearApiKeyMessages());
        
        // Hotkey configuration
        this.hotkeyDisplay.addEventListener('click', () => this.startHotkeyCapture());
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        document.addEventListener('keyup', (e) => this.handleKeyUp(e));
        
        // IPC event listeners from main process
        ipcRenderer.on('recording-started', () => this.onRecordingStarted());
        ipcRenderer.on('recording-stopped', () => this.onRecordingStopped());
        ipcRenderer.on('transcription-complete', (event, text) => this.onTranscriptionComplete(text));
        ipcRenderer.on('ai-response-complete', (event, data) => this.onAIResponseComplete(data));
        
        // Window focus events
        window.addEventListener('focus', () => this.onWindowFocus());
        window.addEventListener('blur', () => this.onWindowBlur());
    }

    async loadConfiguration() {
        try {
            this.config = await ipcRenderer.invoke('get-config');
            this.updateUIFromConfig();
        } catch (error) {
            console.error('Failed to load configuration:', error);
            this.showError('Failed to load configuration');
        }
    }

    updateUIFromConfig() {
        if (!this.config) return;
        
        // Update API key (show masked version)
        if (this.config.openaiApiKey) {
            this.apiKeyInput.value = this.config.openaiApiKey;
        }
        
        // Update language
        this.languageSelect.value = this.config.speechSettings.language || 'en';
        
        // Update hotkey display
        this.updateHotkeyDisplay(this.config.hotkey || 'CommandOrControl+Space');
        
        // Hide settings by default
        this.configSection.style.display = 'none';
    }

    updateHotkeyDisplay(hotkey) {
        // Simple display - just show the key
        this.hotkeyDisplay.textContent = hotkey || 'F2';
    }

    startHotkeyCapture() {
        if (this.isCapturingHotkey) return;
        
        this.isCapturingHotkey = true;
        this.capturedKeys.clear();
        this.hotkeyDisplay.textContent = 'Press keys...';
        this.hotkeyDisplay.style.background = '#fff3cd';
        this.hotkeyDisplay.style.borderColor = '#ffeaa7';
        
        // Auto-cancel after 10 seconds
        setTimeout(() => {
            if (this.isCapturingHotkey) {
                this.cancelHotkeyCapture();
            }
        }, 10000);
    }

    handleKeyDown(event) {
        if (!this.isCapturingHotkey) return;
        
        event.preventDefault();
        event.stopPropagation();
        
        // Add key to captured keys
        if (event.ctrlKey) this.capturedKeys.add('ctrl');
        if (event.shiftKey) this.capturedKeys.add('shift');
        if (event.altKey) this.capturedKeys.add('alt');
        if (event.metaKey) this.capturedKeys.add('cmd');
        
        // Add the main key (not modifier keys)
        if (!['Control', 'Shift', 'Alt', 'Meta'].includes(event.key)) {
            // Map special keys to their proper names
            let keyName = event.key;
            if (keyName === ' ') keyName = 'space';
            else if (keyName === 'Enter') keyName = 'return';
            else if (keyName === 'Escape') keyName = 'escape';
            else if (keyName === 'Tab') keyName = 'tab';
            else if (keyName === 'Backspace') keyName = 'backspace';
            else if (keyName === 'Delete') keyName = 'delete';
            else keyName = keyName.toLowerCase();
            
            this.capturedKeys.add(keyName);
        }
        
        this.updateHotkeyPreview();
    }

    handleKeyUp(event) {
        if (!this.isCapturingHotkey) return;
        
        event.preventDefault();
        event.stopPropagation();
        
        // If this was a modifier key release and we have a complete combination, save it
        if (['Control', 'Shift', 'Alt', 'Meta'].includes(event.key) && this.capturedKeys.size >= 2) {
            this.saveHotkey();
        }
    }

    updateHotkeyPreview() {
        const keys = Array.from(this.capturedKeys);
        const displayKeys = [];
        
        // Order modifiers first
        if (keys.includes('ctrl')) displayKeys.push('Ctrl');
        if (keys.includes('cmd')) displayKeys.push('Cmd');
        if (keys.includes('shift')) displayKeys.push('Shift');
        if (keys.includes('alt')) displayKeys.push('Alt');
        
        // Add main key
        const mainKey = keys.find(k => !['ctrl', 'cmd', 'shift', 'alt'].includes(k));
        if (mainKey) {
            // Format special keys for display
            let displayKey = mainKey;
            if (mainKey === 'space') displayKey = 'Space';
            else if (mainKey === 'return') displayKey = 'Enter';
            else if (mainKey === 'escape') displayKey = 'Esc';
            else if (mainKey === 'tab') displayKey = 'Tab';
            else if (mainKey === 'backspace') displayKey = 'Backspace';
            else if (mainKey === 'delete') displayKey = 'Delete';
            else displayKey = mainKey.toUpperCase();
            
            displayKeys.push(displayKey);
        }
        
        this.hotkeyDisplay.textContent = displayKeys.join(' + ') || 'Press keys...';
    }

    async saveHotkey() {
        const keys = Array.from(this.capturedKeys);
        
        // Convert to Electron format
        let electronKeys = [];
        
        if (keys.includes('ctrl') || keys.includes('cmd')) {
            electronKeys.push('CommandOrControl');
        }
        if (keys.includes('shift')) electronKeys.push('Shift');
        if (keys.includes('alt')) electronKeys.push('Alt');
        
        const mainKey = keys.find(k => !['ctrl', 'cmd', 'shift', 'alt'].includes(k));
        if (mainKey) {
            // Map to Electron accelerator format
            let electronKey = mainKey;
            if (mainKey === 'space') electronKey = 'Space';
            else if (mainKey === 'return') electronKey = 'Return';
            else if (mainKey === 'escape') electronKey = 'Escape';
            else if (mainKey === 'tab') electronKey = 'Tab';
            else if (mainKey === 'backspace') electronKey = 'Backspace';
            else if (mainKey === 'delete') electronKey = 'Delete';
            else electronKey = mainKey.charAt(0).toUpperCase() + mainKey.slice(1);
            
            electronKeys.push(electronKey);
        }
        
        if (electronKeys.length < 2) {
            this.showError('Hotkey must include at least one modifier key');
            this.cancelHotkeyCapture();
            return;
        }
        
        const newHotkey = electronKeys.join('+');
        
        try {
            const success = await ipcRenderer.invoke('update-hotkey', newHotkey);
            if (success) {
                this.config.hotkey = newHotkey;
                this.showSuccess('Hotkey updated successfully');
            } else {
                this.showError('Failed to register hotkey - it may be in use');
            }
        } catch (error) {
            console.error('Failed to update hotkey:', error);
            this.showError('Failed to update hotkey');
        }
        
        this.finishHotkeyCapture();
    }

    cancelHotkeyCapture() {
        this.finishHotkeyCapture();
        this.updateHotkeyDisplay(this.config?.hotkey || 'CommandOrControl+Space');
    }

    finishHotkeyCapture() {
        this.isCapturingHotkey = false;
        this.capturedKeys.clear();
        this.hotkeyDisplay.style.background = '#f7fafc';
        this.hotkeyDisplay.style.borderColor = '#e2e8f0';
    }

    async toggleRecording() {
        try {
            if (this.isRecording) {
                await ipcRenderer.invoke('stop-recording');
            } else {
                await ipcRenderer.invoke('start-recording');
            }
        } catch (error) {
            console.error('Failed to toggle recording:', error);
            this.showError('Failed to toggle recording');
        }
    }

    toggleSettings() {
        const isVisible = this.configSection.style.display !== 'none';
        this.configSection.style.display = isVisible ? 'none' : 'block';
        this.settingsBtn.textContent = isVisible ? 'Settings' : 'Hide Settings';
    }

    async saveConfiguration() {
        try {
            const updatedConfig = {
                openaiApiKey: this.apiKeyInput.value.trim(),
                speechSettings: {
                    ...this.config.speechSettings,
                    language: this.languageSelect.value
                }
            };

            const success = await ipcRenderer.invoke('update-config', updatedConfig);
            
            if (success) {
                this.showSuccess('Configuration saved successfully');
                this.config = await ipcRenderer.invoke('get-config');
            } else {
                this.showError('Failed to save configuration');
            }
        } catch (error) {
            console.error('Failed to save configuration:', error);
            this.showError('Failed to save configuration');
        }
    }

    async validateApiKey() {
        const apiKey = this.apiKeyInput.value.trim();
        
        if (!apiKey) {
            this.clearApiKeyMessages();
            return;
        }

        // Basic format validation
        if (!this.isValidApiKeyFormat(apiKey)) {
            this.showApiKeyError('Invalid API key format');
            return;
        }

        try {
            // Test API key with OpenAI
            const isValid = await ipcRenderer.invoke('test-api-key', apiKey);
            
            if (isValid) {
                this.showApiKeySuccess('API key is valid');
            } else {
                this.showApiKeyError('API key is invalid or expired');
            }
        } catch (error) {
            console.error('API key validation failed:', error);
            this.showApiKeyError('Failed to validate API key');
        }
    }

    isValidApiKeyFormat(apiKey) {
        // OpenAI API keys start with 'sk-' followed by base64-like characters
        // Updated format includes hyphens, underscores, and longer length
        return /^sk-[a-zA-Z0-9_-]{32,}$/.test(apiKey);
    }

    clearApiKeyMessages() {
        this.apiKeyError.classList.add('hidden');
        this.apiKeySuccess.classList.add('hidden');
    }

    showApiKeyError(message) {
        this.apiKeyError.textContent = message;
        this.apiKeyError.classList.remove('hidden');
        this.apiKeySuccess.classList.add('hidden');
    }

    showApiKeySuccess(message) {
        this.apiKeySuccess.textContent = message;
        this.apiKeySuccess.classList.remove('hidden');
        this.apiKeyError.classList.add('hidden');
    }

    onRecordingStarted() {
        this.isRecording = true;
        this.statusDot.classList.add('recording');
        this.statusText.textContent = 'Recording...';
        this.recordBtn.textContent = 'Stop Recording';
        this.recordBtn.classList.remove('btn-primary');
        this.recordBtn.classList.add('btn-secondary');
    }

    onRecordingStopped() {
        this.isRecording = false;
        this.statusDot.classList.remove('recording');
        this.statusText.textContent = 'Processing...';
        this.recordBtn.textContent = 'Start Recording';
        this.recordBtn.classList.remove('btn-secondary');
        this.recordBtn.classList.add('btn-primary');
    }

    onTranscriptionComplete(text) {
        this.statusText.textContent = 'Ready';
        this.transcriptionText.textContent = text;

        // Show a brief success indicator
        this.statusText.textContent = 'Text inserted!';
        setTimeout(() => {
            this.statusText.textContent = 'Ready';
        }, 2000);
    }

    onAIResponseComplete(data) {
        this.statusText.textContent = 'Ready';

        // Display the AI response with mode information
        const displayText = `[${data.mode.toUpperCase()} MODE]\nPrompt: "${data.prompt}"\n\nResponse:\n${data.response}`;
        this.transcriptionText.textContent = displayText;

        // Show success indicator with mode
        this.statusText.textContent = `${data.mode.charAt(0).toUpperCase() + data.mode.slice(1)} response inserted!`;
        setTimeout(() => {
            this.statusText.textContent = 'Ready';
        }, 3000);
    }

    onWindowFocus() {
        // Window gained focus
        console.log('Window focused');
    }

    onWindowBlur() {
        // Window lost focus
        console.log('Window blurred');
    }

    showError(message) {
        // Simple error display - in a real app you'd want a proper notification system
        console.error(message);
        this.statusText.textContent = `Error: ${message}`;
        this.statusText.style.color = '#e53e3e';
        
        setTimeout(() => {
            this.statusText.textContent = 'Ready';
            this.statusText.style.color = '';
        }, 3000);
    }

    showSuccess(message) {
        console.log(message);
        this.statusText.textContent = message;
        this.statusText.style.color = '#38a169';
        
        setTimeout(() => {
            this.statusText.textContent = 'Ready';
            this.statusText.style.color = '';
        }, 2000);
    }
}

// Initialize the renderer app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new RendererApp();
});

// Handle uncaught errors
window.addEventListener('error', (event) => {
    console.error('Renderer error:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
});
